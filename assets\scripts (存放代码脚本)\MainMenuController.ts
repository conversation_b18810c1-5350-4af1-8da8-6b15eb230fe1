import { _decorator, Component, No<PERSON>, <PERSON><PERSON>, director } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MainMenuController')
export class MainMenuController extends Component {
    
    @property(Button)
    startButton: Button = null!;

    start() {
        // 为开始按钮添加点击事件监听
        if (this.startButton) {
            this.startButton.node.on(Button.EventType.CLICK, this.onStartButtonClick, this);
        }
    }

    /**
     * 开始按钮点击事件处理
     */
    onStartButtonClick() {
        console.log("开始游戏按钮被点击了！");
        
        // 这里暂时只打印日志，稍后我们会添加场景跳转
        // director.loadScene("GameScene");
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.startButton) {
            this.startButton.node.off(Button.EventType.CLICK, this.onStartButtonClick, this);
        }
    }
}
